/* style.css */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background: #f2f2f2;
}
header {
  background-color: #4CAF50;
  padding: 20px;
  color: white;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-wrap: wrap; /* Allow items to wrap on smaller screens */
}
header h1 {
  margin: 0;
  display: flex;
  align-items: center;
}
.store-logo {
  height: 50px;
  margin-right: 15px;
  vertical-align: middle;
}
nav {
  margin-top: 10px;
  width: 100%;
}
nav ul {
  list-style-type: none;
  padding: 0;
  display: flex;
  justify-content: center;
  margin: 0;
  flex-wrap: wrap; /* Allow nav items to wrap */
}
nav ul li {
  margin: 0 15px;
}
nav ul li a {
  color: white;
  text-decoration: none;
  padding: 5px 10px;
  transition: background-color 0.3s ease;
}
nav ul li a:hover {
  background-color: #45a049;
  border-radius: 5px;
}
main {
  padding: 20px;
}
footer {
  background-color: #333;
  color: white;
  text-align: center;
  padding: 10px;
  position: relative;
  bottom: 0;
  width: 100%;
}
img {
  margin: 10px;
  border-radius: 8px;
}

/* Optional: Add some styling for the new elements */
.image-gallery, .video-container, .about-images {
    text-align: center;
    margin-bottom: 20px;
}
.image-gallery img, .about-images img {
    max-width: 100%;
    height: auto;
    margin: 10px;
}
.video-container video {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
}
button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 15px;
    margin-right: 10px;
}
button:hover {
    background-color: #45a049;
}

/* Style for product checkboxes and labels */
#product-list {
    list-style: none;
    padding: 0;
}
#product-list li {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#product-list label {
    margin-left: 10px;
    font-size: 1.1em;
    cursor: pointer;
    flex-grow: 1;
}

#product-list input[type="checkbox"] {
    transform: scale(1.3);
    cursor: pointer;
    margin-right: 5px;
}
/* Style for the Contact Info section */
.contact-info {
    background-color: #e9e9e9;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
    margin-bottom: 20px;
}
.contact-info h3 {
    color: #4CAF50;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 5px;
    margin-bottom: 15px;
}
.contact-info p, .contact-info ul {
    margin-bottom: 10px;
    line-height: 1.6;
}
.contact-info ul {
    list-style: inside disc;
    padding-left: 20px;
}

/* Product thumbnail specific style */
.product-thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    margin-right: 15px;
    border-radius: 5px;
}