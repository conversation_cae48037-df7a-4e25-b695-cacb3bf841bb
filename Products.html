<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Products - Kwikspar Dericks</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <header>
    <h1>
<img src="LoGo.jpg" alt="Kwikspar Dericks Logo" class="store-logo">
      Kwikspar Dericks
    </h1>
    <nav>
      <ul>
        <li><a href="index.html">Home</a></li>
        <li><a href="products.html">Products</a></li>
        <li><a href="about.html">About Us</a></li>
        <li><a href="contact.html">Contact Us</a></li>
      </ul>
    </nav>
  </header>

<!-- Main Section -->
  <main>
 <!-- Page Title -->
 <!-- Filters and Search Controls -->
        <div class="filters">
            <input type="text" placeholder="Search products..." id="search-bar">
            <select id="category-filter">
                <option value="all">All Categories</option>
                <option value="vegetables">Vegetables</option>
                <option value="fruits">Fruits</option>
            </select>
        </div>

    <h2>Our Products</h2>
 <!-- This unordered list holds all your products. Its ID is used by JavaScript. -->
    <ul id="product-list">
      <li>
        <img src="Milk.webp" alt="Milk" class="product-thumbnail">
 <!-- The 'onchange' event calls toggleCart() and passes 'this' (the checkbox itself) -->
        <!-- 'value' holds the price, 'data-name' holds the product display name -->
        <input type="checkbox" id="milk" value="20" data-name="Milk" onchange="toggleCart(this)">
<label for="milk">Milk - R20</label>
      </li>
      <li>
        <img src="bread.png" alt="Bread" class="product-thumbnail">
        <input type="checkbox" id="bread" value="15" data-name="Bread" onchange="toggleCart(this)">
        <label for="bread">Bread - R15</label>
      </li>
      <li>
        <img src="Chicken.jpg" alt="Chicken" class="product-thumbnail">
        <input type="checkbox" id="chicken" value="50" data-name="Chicken (1kg)" onchange="toggleCart(this)">
        <label for="chicken">Chicken (1kg) - R50</label>
      </li>
      <li>
        <img src="Apples.webp" alt="Apples" class="product-thumbnail">
        <input type="checkbox" id="apples" value="25" data-name="Apples (1kg)" onchange="toggleCart(this)">
        <label for="apples">Apples (1kg) - R25</label>
      </li>
      <li>
        <img src="Bananas.webp" alt="Bananas" class="product-thumbnail">
        <input type="checkbox" id="bananas" value="18" data-name="Bananas (1kg)" onchange="toggleCart(this)">
        <label for="bananas">Bananas (1kg) - R18</label>
      </li>
      <li>
        <img src="Potatoes.webp" alt="Potatoes" class="product-thumbnail">
        <input type="checkbox" id="potatoes" value="30" data-name="Potatoes (2kg)" onchange="toggleCart(this)">
        <label for="potatoes">Potatoes (2kg) - R30</label>
      </li>
      <li>
        <img src="Tomatoes.jpg" alt="Tomatoes" class="product-thumbnail">
        <input type="checkbox" id="tomatoes" value="22" data-name="Tomatoes (500g)" onchange="toggleCart(this)">
        <label for="tomatoes">Tomatoes (500g) - R22</label>
      </li>
      <li>
        <img src="Spinach.jpg" alt="Spinach" class="product-thumbnail">
        <input type="checkbox" id="spinach" value="12" data-name="Spinach (bunch)" onchange="toggleCart(this)">
        <label for="spinach">Spinach (bunch) - R12</label>
      </li>
      <li>
        <img src="Pears.webp" alt="Pears" class="product-thumbnail">
        <input type="checkbox" id="pears" value="28" data-name="Pears (1kg)" onchange="toggleCart(this)">
        <label for="pears">Pears (1kg) - R28</label>
      </li>
    </ul>

    <h2>Your Shopping Cart</h2>
    <ul id="cart-items">
      <li>Your cart is empty.</li>
    </ul>
    <p>Total: <span id="cart-total">R0.00</span></p>
    <button id="checkout-button" onclick="processOrder(100, 10)">Checkout (Spend R100 for 10% off!)</button> 

  </main>
  <footer>
    <p>&copy; 2025 Kwikspar Dericks. All rights reserved.</p>
 <!-- Footer Section -->
  </footer>
  
</body>
</html>